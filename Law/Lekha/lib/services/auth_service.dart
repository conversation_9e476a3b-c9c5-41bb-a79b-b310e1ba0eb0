import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import 'storage_service.dart';

class AuthService extends ChangeNotifier {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  User? _currentUser;
  bool _isAuthenticated = false;
  bool _isLoading = false;

  User? get currentUser => _currentUser;
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;

  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: [
      'email',
      'profile',
      'https://www.googleapis.com/auth/calendar',
    ],
  );

  final StorageService _storageService = StorageService();

  Future<void> initialize() async {
    _setLoading(true);
    try {
      // Check if user is already logged in
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('current_user_id');
      
      if (userId != null) {
        // Load user from local storage
        final userData = await _storageService.getUser(userId);
        if (userData != null) {
          _currentUser = userData;
          _isAuthenticated = true;
        }
      }
    } catch (e) {
      debugPrint('Error initializing auth service: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> signInWithEmail(String email, String password) async {
    _setLoading(true);
    try {
      // TODO: Implement actual email/password authentication
      // For now, create a mock user
      final user = await _createMockUser(email, UserType.advocate);
      await _setCurrentUser(user);
      return true;
    } catch (e) {
      debugPrint('Error signing in with email: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> signInWithGoogle() async {
    _setLoading(true);
    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        return false; // User cancelled
      }

      // Check if user exists in local storage
      final existingUser = await _storageService.getUserByEmail(googleUser.email);
      
      if (existingUser != null) {
        await _setCurrentUser(existingUser);
        return true;
      } else {
        // New user - redirect to profile creation
        // For now, create a mock user
        final user = await _createMockUser(googleUser.email, UserType.advocate);
        await _setCurrentUser(user);
        return true;
      }
    } catch (e) {
      debugPrint('Error signing in with Google: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> createUser({
    required String email,
    String? password,
    required UserType userType,
    required UserProfile profile,
    required String languagePreference,
  }) async {
    _setLoading(true);
    try {
      final user = User(
        userId: DateTime.now().millisecondsSinceEpoch.toString(),
        email: email,
        passwordHash: password != null ? _hashPassword(password) : null,
        userType: userType,
        languagePreference: languagePreference,
        createdAt: DateTime.now(),
        profile: profile,
        accessControl: _getAccessControlForUserType(userType),
      );

      await _storageService.saveUser(user);
      await _setCurrentUser(user);
      return true;
    } catch (e) {
      debugPrint('Error creating user: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> signOut() async {
    _setLoading(true);
    try {
      await _googleSignIn.signOut();
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_user_id');
      
      _currentUser = null;
      _isAuthenticated = false;
    } catch (e) {
      debugPrint('Error signing out: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateUserProfile(UserProfile profile) async {
    if (_currentUser == null) return false;

    try {
      final updatedUser = User(
        userId: _currentUser!.userId,
        email: _currentUser!.email,
        passwordHash: _currentUser!.passwordHash,
        userType: _currentUser!.userType,
        languagePreference: _currentUser!.languagePreference,
        createdAt: _currentUser!.createdAt,
        lastLogin: DateTime.now(),
        profile: profile,
        accessControl: _currentUser!.accessControl,
      );

      await _storageService.saveUser(updatedUser);
      _currentUser = updatedUser;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error updating user profile: $e');
      return false;
    }
  }

  Future<bool> updateLanguagePreference(String languageCode) async {
    if (_currentUser == null) return false;

    try {
      final updatedUser = User(
        userId: _currentUser!.userId,
        email: _currentUser!.email,
        passwordHash: _currentUser!.passwordHash,
        userType: _currentUser!.userType,
        languagePreference: languageCode,
        createdAt: _currentUser!.createdAt,
        lastLogin: DateTime.now(),
        profile: _currentUser!.profile,
        accessControl: _currentUser!.accessControl,
      );

      await _storageService.saveUser(updatedUser);
      _currentUser = updatedUser;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error updating language preference: $e');
      return false;
    }
  }

  Future<void> _setCurrentUser(User user) async {
    _currentUser = user;
    _isAuthenticated = true;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('current_user_id', user.userId);
    
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  Future<User> _createMockUser(String email, UserType userType) async {
    return User(
      userId: DateTime.now().millisecondsSinceEpoch.toString(),
      email: email,
      userType: userType,
      languagePreference: 'hindi',
      createdAt: DateTime.now(),
      profile: UserProfile(
        name: Name(firstName: 'John', lastName: 'Doe'),
        contact: Contact(email: email, mobile: '+919876543210'),
        address: Address(
          street: '123 Main St',
          city: 'Bengaluru',
          state: 'Karnataka',
          zipCode: '560001',
        ),
        advocateLicenseNumber: userType != UserType.intern ? 'ADV123456' : null,
        officeName: userType != UserType.intern ? 'Legal Eagles Law Firm' : null,
        numberOfEmployees: userType == UserType.advocate ? 5 : null,
        registeredCompanyType: userType == UserType.advocate ? CompanyType.partnership : null,
        roleAtCompany: userType != UserType.intern ? 'Senior Partner' : null,
        advocateStatus: userType != UserType.intern ? AdvocateStatus.active : null,
        age: userType == UserType.intern ? 22 : null,
        college: userType == UserType.intern ? 'National Law School of India University' : null,
        licensesEnrolledStatus: userType == UserType.intern ? LicenseStatus.student : null,
        internRequests: userType == UserType.intern ? [] : null,
      ),
      accessControl: _getAccessControlForUserType(userType),
    );
  }

  AccessControl _getAccessControlForUserType(UserType userType) {
    switch (userType) {
      case UserType.advocate:
        return AccessControl.forAdvocate();
      case UserType.jrAdvocate:
        return AccessControl.forJrAdvocate();
      case UserType.intern:
        return AccessControl.forIntern();
    }
  }

  String _hashPassword(String password) {
    // TODO: Implement proper password hashing
    return password; // Placeholder
  }
}
